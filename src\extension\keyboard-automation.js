/**
 * Chrome扩展键盘自动化模块
 * 支持Tab键导航和键盘输入模拟
 * 兼容无头模式（headless mode）
 */

class KeyboardAutomation {
  constructor() {
    this.isRunning = false;
    this.currentStep = 0;
    this.steps = [];
  }

  /**
   * 模拟键盘按键事件
   * @param {string} key - 按键名称 (如 'Tab', 'Enter', 'a', '1' 等)
   * @param {Element} target - 目标元素，默认为当前焦点元素
   * @param {Object} options - 事件选项
   */
  simulateKeyPress(key, target = null, options = {}) {
    const element = target || document.activeElement || document.body;
    
    const defaultOptions = {
      bubbles: true,
      cancelable: true,
      composed: true,
      ...options
    };

    // 创建键盘事件
    const keydownEvent = new KeyboardEvent('keydown', {
      key: key,
      code: this.getKeyCode(key),
      keyCode: this.getKeyCodeNumber(key),
      which: this.getKeyCodeNumber(key),
      ...defaultOptions
    });

    const keypressEvent = new KeyboardEvent('keypress', {
      key: key,
      code: this.getKeyCode(key),
      keyCode: this.getKeyCodeNumber(key),
      which: this.getKeyCodeNumber(key),
      ...defaultOptions
    });

    const keyupEvent = new KeyboardEvent('keyup', {
      key: key,
      code: this.getKeyCode(key),
      keyCode: this.getKeyCodeNumber(key),
      which: this.getKeyCodeNumber(key),
      ...defaultOptions
    });

    // 依次触发事件
    element.dispatchEvent(keydownEvent);
    
    // 对于可输入字符，触发keypress事件
    if (key.length === 1) {
      element.dispatchEvent(keypressEvent);
    }
    
    element.dispatchEvent(keyupEvent);

    console.log(`🎹 模拟按键: ${key} 在元素:`, element);
  }

  /**
   * 获取按键的Code值
   */
  getKeyCode(key) {
    const keyCodeMap = {
      'Tab': 'Tab',
      'Enter': 'Enter',
      'Escape': 'Escape',
      'Space': 'Space',
      'ArrowUp': 'ArrowUp',
      'ArrowDown': 'ArrowDown',
      'ArrowLeft': 'ArrowLeft',
      'ArrowRight': 'ArrowRight'
    };
    
    if (keyCodeMap[key]) {
      return keyCodeMap[key];
    }
    
    // 对于数字和字母
    if (key.match(/[0-9]/)) {
      return `Digit${key}`;
    }
    if (key.match(/[a-zA-Z]/)) {
      return `Key${key.toUpperCase()}`;
    }
    
    return key;
  }

  /**
   * 获取按键的数字代码
   */
  getKeyCodeNumber(key) {
    const keyCodeNumbers = {
      'Tab': 9,
      'Enter': 13,
      'Escape': 27,
      'Space': 32,
      'ArrowUp': 38,
      'ArrowDown': 40,
      'ArrowLeft': 37,
      'ArrowRight': 39
    };
    
    if (keyCodeNumbers[key]) {
      return keyCodeNumbers[key];
    }
    
    // 数字键
    if (key.match(/[0-9]/)) {
      return 48 + parseInt(key);
    }
    
    // 字母键
    if (key.match(/[a-zA-Z]/)) {
      return key.toUpperCase().charCodeAt(0);
    }
    
    return 0;
  }

  /**
   * 模拟Tab键按下（焦点导航）
   * @param {boolean} shiftKey - 是否同时按下Shift键（反向导航）
   */
  async simulateTab(shiftKey = false) {
    return new Promise((resolve) => {
      // 获取当前焦点元素
      const currentElement = document.activeElement;
      console.log('🎯 当前焦点元素:', currentElement);

      // 模拟Tab键事件
      this.simulateKeyPress('Tab', currentElement, { shiftKey });

      // 等待焦点切换完成
      setTimeout(() => {
        const newElement = document.activeElement;
        console.log('🎯 新焦点元素:', newElement);
        resolve(newElement);
      }, 100);
    });
  }

  /**
   * 在当前焦点元素中输入文本
   * @param {string} text - 要输入的文本
   */
  async simulateTextInput(text) {
    const element = document.activeElement;
    
    if (!element) {
      console.error('❌ 没有焦点元素可以输入');
      return false;
    }

    // 检查元素是否可以输入
    if (!this.isInputElement(element)) {
      console.error('❌ 当前焦点元素不支持文本输入:', element);
      return false;
    }

    // 清空现有内容
    if (element.value !== undefined) {
      element.value = '';
    }

    // 逐字符输入
    for (let char of text) {
      this.simulateKeyPress(char, element);
      
      // 更新元素值
      if (element.value !== undefined) {
        element.value += char;
      }
      
      // 触发input事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      
      // 短暂延迟，模拟真实输入
      await this.delay(50);
    }

    // 触发change事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    console.log(`✅ 输入文本: "${text}" 到元素:`, element);
    return true;
  }

  /**
   * 检查元素是否可以输入文本
   */
  isInputElement(element) {
    if (!element) return false;
    
    const inputTypes = ['input', 'textarea', 'select'];
    const tagName = element.tagName.toLowerCase();
    
    if (inputTypes.includes(tagName)) {
      return true;
    }
    
    // 检查contenteditable属性
    if (element.contentEditable === 'true') {
      return true;
    }
    
    return false;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 执行您要求的具体操作序列
   * 1次Tab -> 输入2002 -> Tab -> 输入8 -> Tab -> Enter
   */
  async executeSpecificSequence() {
    if (this.isRunning) {
      console.log('⚠️ 自动化序列正在运行中...');
      return;
    }

    this.isRunning = true;
    console.log('🚀 开始执行键盘自动化序列...');

    try {
      // 步骤1: 按下1次Tab
      console.log('📍 步骤1: 按下Tab键');
      await this.simulateTab();
      await this.delay(200);

      // 步骤2: 输入2002
      console.log('📍 步骤2: 输入2002');
      await this.simulateTextInput('2002');
      await this.delay(200);

      // 步骤3: 再次按下Tab
      console.log('📍 步骤3: 按下Tab键');
      await this.simulateTab();
      await this.delay(200);

      // 步骤4: 输入8
      console.log('📍 步骤4: 输入8');
      await this.simulateTextInput('8');
      await this.delay(200);

      // 步骤5: 按下Tab
      console.log('📍 步骤5: 按下Tab键');
      await this.simulateTab();
      await this.delay(200);

      // 步骤6: 按下Enter
      console.log('📍 步骤6: 按下Enter键');
      this.simulateKeyPress('Enter');

      console.log('✅ 键盘自动化序列执行完成!');
      
    } catch (error) {
      console.error('❌ 键盘自动化序列执行失败:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 执行自定义步骤序列
   * @param {Array} steps - 步骤数组
   * 每个步骤格式: { type: 'tab'|'input'|'key', value: string, delay: number }
   */
  async executeCustomSequence(steps) {
    if (this.isRunning) {
      console.log('⚠️ 自动化序列正在运行中...');
      return;
    }

    this.isRunning = true;
    console.log('🚀 开始执行自定义键盘序列...');

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        console.log(`📍 步骤${i + 1}:`, step);

        switch (step.type) {
          case 'tab':
            await this.simulateTab(step.shift || false);
            break;
          case 'input':
            await this.simulateTextInput(step.value);
            break;
          case 'key':
            this.simulateKeyPress(step.value);
            break;
          default:
            console.warn('⚠️ 未知步骤类型:', step.type);
        }

        // 步骤间延迟
        if (step.delay) {
          await this.delay(step.delay);
        } else {
          await this.delay(200); // 默认延迟
        }
      }

      console.log('✅ 自定义键盘序列执行完成!');
      
    } catch (error) {
      console.error('❌ 自定义键盘序列执行失败:', error);
    } finally {
      this.isRunning = false;
    }
  }
}

// 创建全局实例
window.keyboardAutomation = new KeyboardAutomation();

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = KeyboardAutomation;
}

console.log('🎹 键盘自动化模块已加载 - 支持无头模式');
