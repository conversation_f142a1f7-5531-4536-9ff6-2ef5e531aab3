/**
 * 强制验证码处理脚本
 */

function forceVerificationCodeProcessing() {
  console.log('🚀 === 强制验证码处理 ===');
  
  // 1. 检查当前页面是否有验证码输入框
  const codeInputs = document.querySelectorAll('[id*="codeEntry"]');
  console.log(`🔍 检测到 ${codeInputs.length} 个验证码输入框`);
  
  if (codeInputs.length === 0) {
    console.log('❌ 当前页面没有验证码输入框');
    return false;
  }
  
  // 2. 强制设置为验证码阶段
  console.log('🔄 强制推进到验证码阶段...');
  if (typeof setCurrentStage !== 'undefined') {
    setCurrentStage('verification_code');
    console.log('✅ 已推进到验证码阶段');
  } else {
    console.log('❌ setCurrentStage函数不存在');
    return false;
  }
  
  // 3. 确保有测试账号
  if (typeof msRegistrationState !== 'undefined' && !msRegistrationState.currentAccount) {
    console.log('🔧 设置测试账号...');
    const testEmail = `test${Date.now().toString().slice(-6)}@outlook.com`;
    msRegistrationState.currentAccount = testEmail;
    
    if (typeof imapService !== 'undefined') {
      imapService.currentEmail = testEmail;
    }
    
    console.log('✅ 测试账号已设置:', testEmail);
  }
  
  // 4. 手动触发验证码处理
  console.log('🎯 手动触发验证码处理...');
  if (typeof handleVerificationCodePage !== 'undefined') {
    handleVerificationCodePage();
    console.log('✅ 验证码处理已触发');
  } else {
    console.log('❌ handleVerificationCodePage函数不存在');
    return false;
  }
  
  // 5. 等待一段时间后检查IMAP状态
  setTimeout(() => {
    console.log('📊 IMAP状态检查:');
    if (typeof imapService !== 'undefined') {
      console.log('  当前邮箱:', imapService.currentEmail);
      console.log('  是否等待验证码:', imapService.isWaitingForCode);
      console.log('  重试次数:', imapService.retryCount);
      
      if (!imapService.isWaitingForCode) {
        console.log('⚠️ IMAP服务未启动，手动启动...');
        if (typeof startImapEmailCheck !== 'undefined' && imapService.currentEmail) {
          startImapEmailCheck(imapService.currentEmail);
          console.log('✅ IMAP服务已启动');
        }
      }
    }
  }, 2000);
  
  return true;
}

// 手动检查验证码
async function manualVerificationCodeCheck() {
  console.log('🔍 === 手动验证码检查 ===');
  
  if (typeof imapService === 'undefined' || !imapService.currentEmail) {
    console.log('❌ IMAP服务或邮箱未设置');
    return;
  }
  
  const email = imapService.currentEmail;
  console.log('📧 检查邮箱:', email);
  
  try {
    const apiUrl = `http://localhost:3000/verification-code/${encodeURIComponent(email)}?timestamp=${Date.now()}`;
    console.log('📡 请求URL:', apiUrl);
    
    const response = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
    
    console.log('📡 响应状态:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('📧 API响应:', result);
      
      if (result.success && result.code) {
        console.log('🎉 找到验证码:', result.code);
        
        // 填入验证码
        fillCodeIntoInputs(result.code);
      } else {
        console.log('⚠️ 没有找到验证码');
      }
    } else {
      console.error('❌ API请求失败:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ 验证码检查失败:', error);
    console.log('💡 请确保IMAP服务器正在运行在 http://localhost:3000');
  }
}

// 填入验证码到输入框
function fillCodeIntoInputs(code) {
  console.log('🎯 填入验证码:', code);
  
  if (code.length !== 6) {
    console.error('❌ 验证码长度不正确:', code.length);
    return false;
  }
  
  let successCount = 0;
  
  for (let i = 0; i < 6; i++) {
    const input = document.querySelector(`#codeEntry-${i}`);
    if (input) {
      input.value = code[i];
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));
      input.dispatchEvent(new Event('keyup', { bubbles: true }));
      successCount++;
      console.log(`  输入框 ${i}: ${code[i]} ✅`);
    } else {
      console.error(`❌ 输入框 ${i} 不存在`);
    }
  }
  
  console.log(`📊 填入结果: ${successCount}/6 个输入框成功`);
  
  if (successCount === 6) {
    console.log('✅ 验证码填入完成，尝试自动提交...');
    
    // 等待一下再提交
    setTimeout(() => {
      const submitButton = document.querySelector('button[type="submit"]');
      if (submitButton && !submitButton.disabled) {
        console.log('🎯 自动提交验证码');
        submitButton.click();
        
        // 推进到下一阶段
        setTimeout(() => {
          if (typeof advanceToNextStage !== 'undefined') {
            advanceToNextStage('verification_code');
            console.log('✅ 已推进到下一阶段');
          }
        }, 1000);
      } else {
        console.log('⚠️ 提交按钮不可用，请手动提交');
      }
    }, 1000);
    
    return true;
  }
  
  return false;
}

// 完整的验证码处理流程
async function fullVerificationCodeFlow() {
  console.log('🚀 === 完整验证码处理流程 ===');
  
  // 1. 强制处理验证码
  const forceResult = forceVerificationCodeProcessing();
  if (!forceResult) {
    console.log('❌ 强制验证码处理失败');
    return;
  }
  
  // 2. 等待IMAP服务启动
  console.log('⏳ 等待IMAP服务启动...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 3. 手动检查验证码
  await manualVerificationCodeCheck();
  
  console.log('🏁 完整流程执行完成');
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.forceVerify = {
    force: forceVerificationCodeProcessing,
    check: manualVerificationCodeCheck,
    fill: fillCodeIntoInputs,
    full: fullVerificationCodeFlow
  };
  
  console.log('💪 强制验证码工具已加载');
  console.log('使用方法:');
  console.log('  forceVerify.force() - 强制验证码处理');
  console.log('  forceVerify.check() - 手动检查验证码');
  console.log('  forceVerify.fill("123456") - 填入验证码');
  console.log('  forceVerify.full() - 完整处理流程');
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    forceVerificationCodeProcessing,
    manualVerificationCodeCheck,
    fillCodeIntoInputs,
    fullVerificationCodeFlow
  };
}
