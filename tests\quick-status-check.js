/**
 * 快速状态检查脚本
 */

function quickStatusCheck() {
  console.log('🔍 === 快速状态检查 ===');
  
  // 1. 检查当前页面状态
  console.log('\n📄 页面信息:');
  console.log('  URL:', window.location.href);
  console.log('  标题:', document.title);
  
  // 2. 检查页面检测结果
  if (typeof detectCurrentPageState !== 'undefined') {
    const pageState = detectCurrentPageState();
    console.log('  检测到的页面类型:', pageState.type);
  } else {
    console.log('  ❌ detectCurrentPageState函数不存在');
  }
  
  // 3. 检查当前状态
  console.log('\n📊 系统状态:');
  if (typeof msRegistrationState !== 'undefined') {
    console.log('  当前步骤:', msRegistrationState.currentStep);
    console.log('  阶段索引:', msRegistrationState.currentStageIndex);
    console.log('  验证状态:', msRegistrationState.verificationStatus);
    console.log('  当前账号:', msRegistrationState.currentAccount);
    console.log('  已完成阶段:', Object.keys(msRegistrationState.stageCompleted).filter(stage => msRegistrationState.stageCompleted[stage]));
  } else {
    console.log('  ❌ msRegistrationState对象不存在');
  }
  
  // 4. 检查IMAP状态
  console.log('\n📧 IMAP状态:');
  if (typeof imapService !== 'undefined') {
    console.log('  当前邮箱:', imapService.currentEmail);
    console.log('  是否等待验证码:', imapService.isWaitingForCode);
    console.log('  重试次数:', imapService.retryCount);
  } else {
    console.log('  ❌ imapService对象不存在');
  }
  
  // 5. 检查页面元素
  console.log('\n🔍 页面元素检查:');
  
  // 检查验证码输入框
  const codeInputs = document.querySelectorAll('[id*="codeEntry"]');
  console.log('  验证码输入框:', codeInputs.length, '个');
  
  // 检查邮件验证相关文本
  const bodyText = document.body.textContent;
  const hasVerifyText = bodyText.includes('验证') || bodyText.includes('verify') || bodyText.includes('code');
  console.log('  包含验证相关文本:', hasVerifyText);
  
  // 检查注册表单
  const emailInputs = document.querySelectorAll('input[type="email"], input[name*="email"], input[id*="email"]');
  console.log('  邮箱输入框:', emailInputs.length, '个');
  
  const passwordInputs = document.querySelectorAll('input[type="password"]');
  console.log('  密码输入框:', passwordInputs.length, '个');
  
  // 6. 建议下一步操作
  console.log('\n💡 建议操作:');
  
  if (typeof msRegistrationState !== 'undefined') {
    const currentStep = msRegistrationState.currentStep;
    const currentAccount = msRegistrationState.currentAccount;
    
    if (!currentAccount) {
      console.log('  ⚠️ 没有当前账号，建议设置测试账号');
      console.log('  执行: testAccount.set()');
    } else if (currentStep === 'email_verification' && codeInputs.length > 0) {
      console.log('  🎯 当前在邮件验证阶段但页面有验证码输入框');
      console.log('  建议: 手动推进到验证码阶段');
      console.log('  执行: setCurrentStage("verification_code")');
    } else if (currentStep === 'email_verification' && codeInputs.length === 0) {
      console.log('  ✅ 当前在邮件验证阶段，等待验证码页面出现');
      console.log('  建议: 检查IMAP服务或手动触发验证码检查');
      console.log('  执行: verifyDiag.manual()');
    } else if (currentStep === 'verification_code' && codeInputs.length > 0) {
      console.log('  ✅ 当前在验证码阶段且页面有输入框');
      console.log('  建议: 手动检查验证码或模拟输入');
      console.log('  执行: verifyDiag.manual() 或 verifyDiag.simulate("123456")');
    } else {
      console.log('  🤔 状态和页面不匹配，建议重新检测');
      console.log('  执行: 刷新页面或重新启动流程');
    }
  }
  
  console.log('\n🏁 检查完成');
}

// 快速修复函数
function quickFix() {
  console.log('🔧 === 快速修复 ===');
  
  // 检查当前页面是否有验证码输入框
  const codeInputs = document.querySelectorAll('[id*="codeEntry"]');
  
  if (codeInputs.length > 0) {
    console.log('🎯 检测到验证码输入框，推进到验证码阶段');
    if (typeof setCurrentStage !== 'undefined') {
      setCurrentStage('verification_code');
      console.log('✅ 已推进到验证码阶段');
    } else {
      console.log('❌ setCurrentStage函数不存在');
    }
  } else {
    console.log('⚠️ 没有检测到验证码输入框');
  }
  
  // 检查是否需要设置账号
  if (typeof msRegistrationState !== 'undefined' && !msRegistrationState.currentAccount) {
    console.log('🔧 没有当前账号，生成测试账号');
    if (typeof testAccount !== 'undefined') {
      testAccount.set();
      console.log('✅ 已设置测试账号');
    } else {
      console.log('❌ testAccount工具不存在');
    }
  }
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.quickCheck = quickStatusCheck;
  window.quickFix = quickFix;
  
  console.log('⚡ 快速检查工具已加载');
  console.log('使用方法:');
  console.log('  quickCheck() - 快速状态检查');
  console.log('  quickFix() - 快速修复');
}

// 自动运行一次检查
if (typeof window !== 'undefined') {
  setTimeout(quickStatusCheck, 1000);
}
